generator client {
  provider = "prisma-client-js"
  output   = "../generated/prisma"
}

datasource db {
  provider = "postgresql"
  url      = env("DATABASE_URL")
}

model shortintro {
  id         String   @id @default(dbgenerated("uuid_generate_v4()")) @db.Uuid
  intro      String?  @db.VarChar(250)
  skills     String[]
  bio        String?
  profilepic String?  @db.VarChar(250)
}

model headline {
  id           String  @id @default(dbgenerated("uuid_generate_v4()")) @db.Uuid
  mainheadline String? @db.VarChar(250)
  subheadline  String? @db.VarChar(250)
}
