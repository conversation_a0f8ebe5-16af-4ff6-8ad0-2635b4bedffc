import { NextRequest, NextResponse } from "next/server";
import { PrismaClient } from "../../../../generated/prisma";

export type HeadlineType = {
    id: string | null,
    mainheadline: string | null,
    subheadline: string | null
}

const prisma = new PrismaClient();

export async function POST(req:NextRequest){
    const formData = await req.formData();
    const retrieve = [...formData.entries()];
    const refactor = retrieve.reduce((acc,current)=>{
        const [key,value] = current;
        
        (acc as any)[key] = value;

        return acc;
    },{} as HeadlineType);

    try{
        await prisma.headline.create({
            data: {
                mainheadline: refactor.mainheadline,
                subheadline : refactor.subheadline
            }
        });

        return NextResponse.json({message:"headline added"},{status:200});
    }catch(error){
        return NextResponse.json(error);
    }
}