import { refactor } from "@/lib/helper";
import { NextRequest } from "next/server";

type UserRoleType = {
    id: string | null,
    logo: string | null,
    title: string | null,
    description: string | null
}

export async function POST(req:NextRequest){
    const formData = await req.formData();
    const retrieve = [...formData.entries()];
    const refactorData : UserRoleType = refactor(retrieve);
}